import React, { useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Link,
  Alert,
  Divider,
  Paper
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import AvatarSelector from '../common/AvatarSelector';

const Register = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showAvatarSelection, setShowAvatarSelection] = useState(false);
  const [avatarData, setAvatarData] = useState({ type: 'initials' });
  const [saving, setSaving] = useState(false);
  const navigate = useNavigate();
  const { registerLegacy, setUser } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setError('');
      setLoading(true);
      const success = await registerLegacy(username, email, password, firstName, lastName);
      if (success) {
        // Show avatar selection after successful registration
        setShowAvatarSelection(true);
        setLoading(false);
      } else {
        setError('Failed to create an account. Please try again.');
        setLoading(false);
      }
    } catch (error) {
      setError('Failed to create an account. Please try again.');
      setLoading(false);
    }
  };

  const handleAvatarChange = (newAvatarData) => {
    // Handle color-only changes vs full avatar changes
    if (newAvatarData.color && Object.keys(newAvatarData).length === 1) {
      // Color-only change, merge with existing avatar data
      setAvatarData(prev => ({ ...prev, color: newAvatarData.color }));
    } else {
      // Full avatar change
      setAvatarData(newAvatarData);
    }
  };

  const handleSaveAvatar = async () => {
    setSaving(true);
    try {
      // Always save the color to the user profile if it was changed from default
      if (avatarData.color && avatarData.color !== '#1976d2') {
        const profileResponse = await fetch('/api/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({ color: avatarData.color })
        });

        if (!profileResponse.ok) {
          throw new Error('Failed to update profile color');
        }
      }

      // Then save avatar data if it changed from default
      if (avatarData.type === 'upload' && avatarData.file) {
        // Handle file upload
        const formData = new FormData();
        formData.append('avatar', avatarData.file);

        const uploadResponse = await fetch('/api/users/avatar/upload', {
          method: 'POST',
          headers: {
            'x-auth-token': localStorage.getItem('token')
          },
          body: formData
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload avatar');
        }
      } else if (avatarData.type === 'icon') {
        // Handle icon selection
        const avatarResponse = await fetch('/api/users/avatar', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({
            type: avatarData.type,
            iconName: avatarData.iconName
          })
        });

        if (!avatarResponse.ok) {
          throw new Error('Failed to update avatar');
        }
      } else if (avatarData.type === 'initials') {
        // Handle initials selection (just update the type, color is already saved above)
        const avatarResponse = await fetch('/api/users/avatar', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({
            type: 'initials'
          })
        });

        if (!avatarResponse.ok) {
          throw new Error('Failed to update avatar');
        }
      }

      // Refresh user data to get updated avatar and color info
      const response = await fetch('/api/auth/user', {
        headers: {
          'x-auth-token': localStorage.getItem('token')
        }
      });
      if (response.ok) {
        const updatedUser = await response.json();
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Failed to save avatar:', error);
    } finally {
      setSaving(false);
    }

    // Navigate to dashboard after avatar selection
    navigate('/');
  };

  const handleSkipAvatar = async () => {
    // Save color if it was changed, even when skipping avatar selection
    if (avatarData.color && avatarData.color !== '#1976d2') {
      try {
        const profileResponse = await fetch('/api/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({ color: avatarData.color })
        });

        if (profileResponse.ok) {
          // Refresh user data to get updated color
          const response = await fetch('/api/auth/user', {
            headers: {
              'x-auth-token': localStorage.getItem('token')
            }
          });
          if (response.ok) {
            const updatedUser = await response.json();
            setUser(updatedUser);
          }
        }
      } catch (error) {
        console.error('Failed to save color on skip:', error);
      }
    }

    // Navigate to dashboard
    navigate('/');
  };

  if (showAvatarSelection) {
    return (
      <Container component="main" maxWidth="md">
        <Box
          sx={{
            marginTop: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography component="h1" variant="h4" sx={{ mb: 3 }}>
            Choose Your Avatar
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
            Personalize your profile by selecting an avatar (optional)
          </Typography>
          <Paper sx={{ p: 3, width: '100%', maxWidth: 600 }}>
            <AvatarSelector
              user={{ username, firstName, lastName, avatar: avatarData, color: avatarData.color }}
              onAvatarChange={handleAvatarChange}
              onClose={() => {}} // Don't close on avatar change
            />
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="outlined"
                onClick={handleSkipAvatar}
                sx={{ mr: 2 }}
                disabled={saving}
              >
                Skip for Now
              </Button>
              <Button
                variant="contained"
                onClick={handleSaveAvatar}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save & Continue'}
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h5">
          Sign up
        </Typography>
        {error && (
          <Alert severity="error" sx={{ mt: 2, width: '100%' }}>
            {error}
          </Alert>
        )}
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
          <Box sx={{ display: 'flex', gap: 2, mb: 1 }}>
            <TextField
              margin="normal"
              fullWidth
              id="firstName"
              label="First Name"
              name="firstName"
              autoComplete="given-name"
              autoFocus
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
            <TextField
              margin="normal"
              fullWidth
              id="lastName"
              label="Last Name"
              name="lastName"
              autoComplete="family-name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
            />
          </Box>
          <TextField
            margin="normal"
            required
            fullWidth
            id="username"
            label="Username"
            name="username"
            autoComplete="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
          />
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            autoComplete="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            autoComplete="new-password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            Sign Up
          </Button>
          <Box sx={{ textAlign: 'center' }}>
            <Link component={RouterLink} to="/login" variant="body2">
              {"Already have an account? Sign In"}
            </Link>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default Register;