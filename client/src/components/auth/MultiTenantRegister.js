import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Link,
  Alert,
  Paper,
  Stepper,
  Step,
  StepLabel,
  FormControlLabel,
  Switch,
  Divider
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import GroupTierSelector from '../group/GroupTierSelector';
import AvatarSelector from '../common/AvatarSelector';

const MultiTenantRegister = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { register, setUser, isAuthenticated, user } = useAuth();

  // Step 1: Group Setup
  const [isNewGroup, setIsNewGroup] = useState(true);
  const [groupName, setGroupName] = useState('');
  const [groupCode, setGroupCode] = useState('');
  const [suggestedCode, setSuggestedCode] = useState('');
  const [selectedTier, setSelectedTier] = useState('1-3');

  // Step 2: User Details
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Step 3: Avatar
  const [avatarData, setAvatarData] = useState({ type: 'initials' });
  const [saving, setSaving] = useState(false);

  const steps = ['Group Setup', 'User Details', 'Avatar Selection'];

  // Use useRef to store the timeout ID
  const debounceTimeoutRef = useRef(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const generateGroupCode = useCallback(async (name) => {
    if (!name.trim()) return;

    try {
      const response = await fetch('/api/groups/suggest-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name })
      });

      if (response.ok) {
        const data = await response.json();
        setSuggestedCode(data.suggestedCode);
        if (!groupCode) {
          setGroupCode(data.suggestedCode);
        }
      }
    } catch (error) {
      console.error('Error generating group code:', error);
    }
  }, [groupCode]);

  const handleGroupNameChange = useCallback((e) => {
    const name = e.target.value;
    setGroupName(name);

    // Generate code suggestion after user stops typing
    if (isNewGroup) {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout with longer delay for better multi-word typing
      debounceTimeoutRef.current = setTimeout(() => {
        generateGroupCode(name);
      }, 1000); // Increased to 1 second for better UX
    }
  }, [isNewGroup, generateGroupCode]);

  const handleNext = async () => {
    setError('');

    if (activeStep === 0) {
      // Validate group setup
      if (isNewGroup) {
        if (!groupName.trim()) {
          setError('Group name is required');
          return;
        }
        if (!groupCode.trim()) {
          setError('Group code is required');
          return;
        }
        if (!selectedTier) {
          setError('Please select a group tier');
          return;
        }
      } else {
        if (!groupCode.trim()) {
          setError('Group code is required to join an existing group');
          return;
        }
      }
    } else if (activeStep === 1) {
      // Validate user details
      if (!firstName.trim() || !lastName.trim()) {
        setError('First and last name are required');
        return;
      }
      if (!username.trim()) {
        setError('Username is required');
        return;
      }
      if (!email.trim()) {
        setError('Email is required');
        return;
      }
      if (!password.trim()) {
        setError('Password is required');
        return;
      }

      // For step 1 (User Details), always attempt registration
      await handleRegistration();
      return;
    }

    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleRegistration = async () => {
    try {
      setLoading(true);
      setError('');

      const registrationData = {
        username,
        email,
        password,
        firstName,
        lastName,
        isNewGroup,
        groupCode
      };

      if (isNewGroup) {
        registrationData.groupName = groupName;
        registrationData.tier = selectedTier;
      }

      const result = await register(registrationData);

      if (result.success) {
        // Wait a moment for authentication state to settle
        await new Promise(resolve => setTimeout(resolve, 100));
        // Both new groups and existing groups go to avatar selection
        setActiveStep(2);
      } else {
        setError(result.error);
      }
    } catch (error) {
      setError('Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (newAvatarData) => {
    if (newAvatarData.color && Object.keys(newAvatarData).length === 1) {
      setAvatarData(prev => ({ ...prev, color: newAvatarData.color }));
    } else {
      setAvatarData(newAvatarData);
    }
  };

  const handleSaveAvatar = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Save avatar and color (similar to existing Register component)
      if (avatarData.color && avatarData.color !== '#1976d2') {
        const profileResponse = await fetch('/api/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          },
          body: JSON.stringify({ color: avatarData.color })
        });

        if (!profileResponse.ok) {
          throw new Error('Failed to update profile color');
        }
      }

      // Handle different avatar types
      if (avatarData.type === 'upload' && avatarData.file) {
        const formData = new FormData();
        formData.append('avatar', avatarData.file);

        const uploadResponse = await fetch('/api/users/avatar/upload', {
          method: 'POST',
          headers: {
            'x-auth-token': localStorage.getItem('token')
          },
          body: formData
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload avatar');
        }
      } else if (avatarData.type === 'icon') {
        const avatarResponse = await fetch('/api/users/avatar', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({
            type: avatarData.type,
            iconName: avatarData.iconName
          })
        });

        if (!avatarResponse.ok) {
          throw new Error('Failed to update avatar');
        }
      } else if (avatarData.type === 'initials') {
        const avatarResponse = await fetch('/api/users/avatar', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({
            type: 'initials'
          })
        });

        if (!avatarResponse.ok) {
          throw new Error('Failed to update avatar');
        }
      }

      // Refresh user data
      const response = await fetch('/api/auth/user', {
        headers: {
          'x-auth-token': localStorage.getItem('token')
        }
      });
      if (response.ok) {
        const updatedUser = await response.json();
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Failed to save avatar:', error);
    } finally {
      setSaving(false);
    }

    navigate('/');
  };

  const handleSkipAvatar = async () => {
    if (avatarData.color && avatarData.color !== '#1976d2') {
      try {
        const profileResponse = await fetch('/api/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          },
          body: JSON.stringify({ color: avatarData.color })
        });

        if (profileResponse.ok) {
          const response = await fetch('/api/auth/user', {
            headers: {
              'x-auth-token': localStorage.getItem('token')
            }
          });
          if (response.ok) {
            const updatedUser = await response.json();
            setUser(updatedUser);
          }
        }
      } catch (error) {
        console.error('Failed to save color on skip:', error);
      }
    }

    navigate('/');
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Group Setup
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={isNewGroup}
                  onChange={(e) => setIsNewGroup(e.target.checked)}
                />
              }
              label={isNewGroup ? "Create a new group" : "Join an existing group"}
              sx={{ mb: 3 }}
            />

            {isNewGroup ? (
              <Box>
                <TextField
                  fullWidth
                  label="Group Name"
                  value={groupName}
                  onChange={handleGroupNameChange}
                  placeholder="e.g., Acme Corporation"
                  sx={{ mb: 2 }}
                  required
                />
                
                <TextField
                  fullWidth
                  label="Group Code"
                  value={groupCode}
                  onChange={(e) => setGroupCode(e.target.value)}
                  placeholder="e.g., acme-corp"
                  helperText="This will be used for user logins (username@groupcode)"
                  sx={{ mb: 3 }}
                  required
                />

                <Divider sx={{ my: 3 }} />

                <GroupTierSelector
                  selectedTier={selectedTier}
                  onTierChange={setSelectedTier}
                />
              </Box>
            ) : (
              <TextField
                fullWidth
                label="Group Code"
                value={groupCode}
                onChange={(e) => setGroupCode(e.target.value)}
                placeholder="Enter the group code provided by your administrator"
                helperText="Contact your group administrator for the group code"
                required
              />
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              User Details
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                fullWidth
                label="First Name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
              <TextField
                fullWidth
                label="Last Name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
              />
            </Box>

            <TextField
              fullWidth
              label="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              helperText={`Your login will be: ${username}@${groupCode}`}
              sx={{ mb: 2 }}
              required
            />

            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            <TextField
              fullWidth
              label="Password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Choose Your Avatar
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Personalize your profile by selecting an avatar (optional)
            </Typography>
            
            <AvatarSelector
              user={{ username, firstName, lastName, avatar: avatarData, color: avatarData.color }}
              onAvatarChange={handleAvatarChange}
              onClose={() => {}}
            />
            
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="outlined"
                onClick={handleSkipAvatar}
                sx={{ mr: 2 }}
                disabled={saving}
              >
                Skip for Now
              </Button>
              <Button
                variant="contained"
                onClick={handleSaveAvatar}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save & Continue'}
              </Button>
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Container component="main" maxWidth="md">
      <Box
        sx={{
          marginTop: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" sx={{ mb: 3 }}>
          Create Your Account
        </Typography>

        <Paper sx={{ width: '100%', p: 4 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {renderStepContent(activeStep)}

          {activeStep < 2 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
              >
                Back
              </Button>
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={loading}
              >
                {loading ? 'Processing...' :
                 activeStep === 1 ? 'Create Account' : 'Next'}
              </Button>
            </Box>
          )}
        </Paper>

        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Link component={RouterLink} to="/login" variant="body2">
            Already have an account? Sign In
          </Link>
        </Box>
      </Box>
    </Container>
  );
};

export default MultiTenantRegister;
