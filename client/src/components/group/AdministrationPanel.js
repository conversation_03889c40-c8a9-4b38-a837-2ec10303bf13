import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Add,
  Delete,
  AdminPanelSettings,
  Person,
  Edit,
  Group,
  People
} from '@mui/icons-material';
import { useGroup } from '../../contexts/GroupContext';
import { useAuth } from '../../contexts/AuthContext';
import EnhancedAvatar from '../common/EnhancedAvatar';

const AdministrationPanel = ({ open, onClose }) => {
  const { group, groupMembers, isAdmin, addGroupMembers, removeGroupMember, updateMemberRole, updateGroupSettings } = useGroup();
  const { user: currentUser } = useAuth();
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [editSettingsDialogOpen, setEditSettingsDialogOpen] = useState(false);
  const [newUsers, setNewUsers] = useState([{ email: '', firstName: '', lastName: '', username: '' }]);
  const [welcomeMessage, setWelcomeMessage] = useState('');
  const [newTier, setNewTier] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null, user: null });

  useEffect(() => {
    if (group) {
      setWelcomeMessage(group.welcomeMessage || '');
      setNewTier(group.tier || '');
    }
  }, [group]);

  const handleAddUser = () => {
    setNewUsers([...newUsers, { email: '', firstName: '', lastName: '', username: '' }]);
  };

  const handleRemoveUser = (index) => {
    setNewUsers(newUsers.filter((_, i) => i !== index));
  };

  const handleUserChange = (index, field, value) => {
    const updated = [...newUsers];
    updated[index][field] = value;
    setNewUsers(updated);
  };

  const handleSubmitUsers = async () => {
    try {
      setLoading(true);
      setError('');

      // Validate users
      const validUsers = newUsers.filter(user => 
        user.email && user.firstName && user.lastName && user.username
      );

      if (validUsers.length === 0) {
        setError('Please fill in all fields for at least one user');
        return;
      }

      await addGroupMembers(validUsers, welcomeMessage);
      
      // Reset form
      setNewUsers([{ email: '', firstName: '', lastName: '', username: '' }]);
      setAddUserDialogOpen(false);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to add users');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = (user) => {
    setConfirmDialog({
      open: true,
      action: 'remove',
      user: user
    });
  };

  const handleConfirmRemove = async () => {
    try {
      await removeGroupMember(confirmDialog.user._id);
      setConfirmDialog({ open: false, action: null, user: null });
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to remove member');
      setConfirmDialog({ open: false, action: null, user: null });
    }
  };

  const handlePromoteToAdmin = async (userId) => {
    try {
      await updateMemberRole(userId, 'administrator');
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to update member role');
    }
  };

  const handleUpdateSettings = async () => {
    try {
      setLoading(true);
      setError('');

      await updateGroupSettings({
        welcomeMessage,
        tier: newTier
      });

      setEditSettingsDialogOpen(false);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Alert severity="warning">
            You don't have administrator privileges for this group.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  const activeMembers = groupMembers.filter(m => m.status === 'active').length;
  const availableSeats = group ? group.maxUsers - activeMembers : 0;

  // Helper functions for determining allowed actions
  const getAdminCount = () => {
    return groupMembers.filter(m => m.role === 'administrator' && m.status === 'active').length;
  };

  const canRemoveUser = (member) => {
    // Super users cannot remove themselves
    if (currentUser?.isSuperUser && member.user._id === currentUser.id) {
      return false;
    }

    // Cannot remove the last administrator
    if (member.role === 'administrator' && getAdminCount() <= 1) {
      return false;
    }

    // Current user cannot remove themselves if they're the only admin
    if (member.user._id === currentUser?.id && member.role === 'administrator' && getAdminCount() <= 1) {
      return false;
    }

    return true;
  };

  const getRemovalWarning = (member) => {
    if (currentUser?.isSuperUser && member.user._id === currentUser.id) {
      return 'Super users cannot remove themselves from groups.';
    }

    if (member.role === 'administrator' && getAdminCount() <= 1) {
      return 'Cannot remove the last administrator. Promote another user first.';
    }

    if (member.user._id === currentUser?.id && member.role === 'administrator' && getAdminCount() <= 1) {
      return 'You cannot remove yourself as the only administrator. Promote another user first.';
    }

    if (member.user._id === currentUser?.id) {
      return 'You are about to remove yourself from this group. You will lose access to group administration.';
    }

    return `Remove ${member.user.firstName} ${member.user.lastName} from the group?`;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AdminPanelSettings />
          Group Administration - {group?.name}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Group Overview */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <People color="primary" />
                  <Box>
                    <Typography variant="h6">{activeMembers}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Members
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Group color="success" />
                  <Box>
                    <Typography variant="h6">{availableSeats}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Available Seats
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AdminPanelSettings color="warning" />
                  <Box>
                    <Typography variant="h6">{group?.tier}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Tier
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Group Members</Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<Edit />}
              onClick={() => setEditSettingsDialogOpen(true)}
              sx={{ mr: 1 }}
            >
              Settings
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setAddUserDialogOpen(true)}
              disabled={availableSeats <= 0}
            >
              Add Users
            </Button>
          </Box>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groupMembers.map((member) => (
                <TableRow key={member._id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <EnhancedAvatar
                        user={member.user}
                        size={32}
                        showInactive={member.status === 'inactive'}
                      />
                      <Box>
                        <Typography variant="body2">
                          {member.user.firstName} {member.user.lastName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          @{member.user.username}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{member.user.email}</TableCell>
                  <TableCell>
                    <Chip
                      icon={member.role === 'administrator' ? <AdminPanelSettings /> : <Person />}
                      label={member.role}
                      color={member.role === 'administrator' ? 'primary' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={member.status}
                      color={member.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {member.role !== 'administrator' && member.status === 'active' && (
                      <IconButton
                        size="small"
                        onClick={() => handlePromoteToAdmin(member.user._id)}
                        title="Promote to Administrator"
                      >
                        <AdminPanelSettings />
                      </IconButton>
                    )}
                    {member.status === 'active' && canRemoveUser(member) && (
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveMember(member)}
                        title="Remove from Group"
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    )}
                    {member.status === 'active' && !canRemoveUser(member) && (
                      <IconButton
                        size="small"
                        disabled
                        title={getRemovalWarning(member)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>

      {/* Add Users Dialog */}
      <Dialog open={addUserDialogOpen} onClose={() => setAddUserDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Users</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Available seats: {availableSeats}
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={3}
            label="Welcome Message"
            value={welcomeMessage}
            onChange={(e) => setWelcomeMessage(e.target.value)}
            placeholder="Welcome to our team! We're excited to have you on board."
            sx={{ mb: 3 }}
          />

          <Divider sx={{ mb: 2 }} />

          {newUsers.map((user, index) => (
            <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle2">User {index + 1}</Typography>
                {newUsers.length > 1 && (
                  <IconButton size="small" onClick={() => handleRemoveUser(index)}>
                    <Delete />
                  </IconButton>
                )}
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={user.firstName}
                    onChange={(e) => handleUserChange(index, 'firstName', e.target.value)}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={user.lastName}
                    onChange={(e) => handleUserChange(index, 'lastName', e.target.value)}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={user.username}
                    onChange={(e) => handleUserChange(index, 'username', e.target.value)}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={user.email}
                    onChange={(e) => handleUserChange(index, 'email', e.target.value)}
                    required
                  />
                </Grid>
              </Grid>
            </Box>
          ))}

          <Button
            startIcon={<Add />}
            onClick={handleAddUser}
            disabled={newUsers.length >= availableSeats}
          >
            Add Another User
          </Button>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddUserDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSubmitUsers}
            disabled={loading}
          >
            {loading ? 'Adding...' : 'Add Users'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Settings Dialog */}
      <Dialog open={editSettingsDialogOpen} onClose={() => setEditSettingsDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Group Settings</DialogTitle>
        <DialogContent sx={{ pt: 4, pb: 3 }}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Welcome Message"
            value={welcomeMessage}
            onChange={(e) => setWelcomeMessage(e.target.value)}
            sx={{ mt: 2, mb: 3 }}
          />

          <FormControl fullWidth>
            <InputLabel>Group Tier</InputLabel>
            <Select
              value={newTier}
              onChange={(e) => setNewTier(e.target.value)}
              label="Group Tier"
            >
              <MenuItem value="1-3">Small Team (1-3 users)</MenuItem>
              <MenuItem value="4-25">Growing Business (4-25 users)</MenuItem>
              <MenuItem value="26-100">Enterprise (26-100 users)</MenuItem>
              <MenuItem value="100+">Enterprise Plus (100+ users)</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditSettingsDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleUpdateSettings}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog.open} onClose={() => setConfirmDialog({ open: false, action: null, user: null })}>
        <DialogTitle>Confirm Action</DialogTitle>
        <DialogContent>
          <Typography>
            {confirmDialog.user && getRemovalWarning(confirmDialog.user)}
          </Typography>
          {confirmDialog.user && confirmDialog.user.user._id === currentUser?.id && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <strong>Warning:</strong> You are about to remove yourself from this group.
              You will lose access to group administration and will need to be re-invited by another administrator.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ open: false, action: null, user: null })}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmRemove}
            color="error"
            variant="contained"
            disabled={confirmDialog.user && !canRemoveUser(confirmDialog.user)}
          >
            Remove User
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default AdministrationPanel;
