import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Chip,
  Grid
} from '@mui/material';
import { People, Business, Domain, Public } from '@mui/icons-material';

const GroupTierSelector = ({ selectedTier, onTierChange }) => {
  const tiers = [
    {
      id: '1-3',
      name: 'Small Team',
      description: 'Perfect for small teams and startups',
      userCount: '1-3 users',
      icon: <People sx={{ fontSize: 40, color: '#4caf50' }} />,
      color: '#4caf50',
      features: ['Basic project management', 'Team collaboration', 'Standard support']
    },
    {
      id: '4-25',
      name: 'Growing Business',
      description: 'Ideal for growing businesses and departments',
      userCount: '4-25 users',
      icon: <Business sx={{ fontSize: 40, color: '#2196f3' }} />,
      color: '#2196f3',
      features: ['Advanced features', 'Priority support', 'Team analytics']
    },
    {
      id: '26-100',
      name: 'Enterprise',
      description: 'For larger organizations and enterprises',
      userCount: '26-100 users',
      icon: <Domain sx={{ fontSize: 40, color: '#ff9800' }} />,
      color: '#ff9800',
      features: ['Enterprise features', 'Dedicated support', 'Advanced analytics', 'Custom integrations']
    },
    {
      id: '100+',
      name: 'Enterprise Plus',
      description: 'For large-scale organizations',
      userCount: '100+ users',
      icon: <Public sx={{ fontSize: 40, color: '#9c27b0' }} />,
      color: '#9c27b0',
      features: ['All features', '24/7 support', 'Custom solutions', 'Dedicated account manager']
    }
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Choose Your Group Size
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Select the tier that best fits your team size. You can change this later.
      </Typography>
      
      <FormControl component="fieldset" fullWidth>
        <RadioGroup
          value={selectedTier}
          onChange={(e) => onTierChange(e.target.value)}
        >
          <Grid container spacing={2}>
            {tiers.map((tier) => (
              <Grid item xs={12} sm={6} key={tier.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    border: selectedTier === tier.id ? `2px solid ${tier.color}` : '1px solid #e0e0e0',
                    '&:hover': {
                      boxShadow: 3
                    }
                  }}
                  onClick={() => onTierChange(tier.id)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                      <FormControlLabel
                        value={tier.id}
                        control={<Radio sx={{ color: tier.color }} />}
                        label=""
                        sx={{ mr: 1, mt: 0 }}
                      />
                      <Box sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          {tier.icon}
                          <Box sx={{ ml: 2 }}>
                            <Typography variant="h6" component="div">
                              {tier.name}
                            </Typography>
                            <Chip 
                              label={tier.userCount} 
                              size="small" 
                              sx={{ 
                                backgroundColor: tier.color,
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {tier.description}
                        </Typography>
                        <Box>
                          {tier.features.map((feature, index) => (
                            <Typography 
                              key={index}
                              variant="caption" 
                              display="block"
                              sx={{ 
                                color: 'text.secondary',
                                '&:before': {
                                  content: '"• "',
                                  color: tier.color,
                                  fontWeight: 'bold'
                                }
                              }}
                            >
                              {feature}
                            </Typography>
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default GroupTierSelector;
