import React, { useState } from 'react';
import { Avatar } from '@mui/material';
import {
  Person as PersonIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  Home as HomeIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Sports as SportsIcon,
  Pets as PetsIcon,
  Flight as FlightIcon,
  DirectionsCar as CarIcon,
  Camera as CameraIcon,
  MusicNote as MusicIcon,
  Palette as PaletteIcon,
  Code as CodeIcon,
  Science as ScienceIcon,
  Restaurant as RestaurantIcon
} from '@mui/icons-material';

// Available free icons for avatar selection
export const AVATAR_ICONS = {
  person: PersonIcon,
  star: StarIcon,
  heart: FavoriteIcon,
  home: HomeIcon,
  work: WorkIcon,
  school: SchoolIcon,
  sports: SportsIcon,
  pets: PetsIcon,
  travel: FlightIcon,
  car: CarIcon,
  camera: CameraIcon,
  music: MusicIcon,
  art: PaletteIcon,
  code: CodeIcon,
  science: ScienceIcon,
  food: RestaurantIcon
};

// Generate initials from user data
const getInitials = (user) => {
  if (user.firstName && user.lastName) {
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  } else if (user.firstName) {
    return user.firstName.substring(0, 2).toUpperCase();
  } else if (user.username) {
    return user.username.substring(0, 2).toUpperCase();
  } else {
    return 'U';
  }
};

const EnhancedAvatar = ({
  user,
  size = 40,
  onClick,
  sx = {},
  showInactive = false,
  ...props
}) => {
  const [imageError, setImageError] = useState(false);

  // Handle image load error - fallback to initials
  const handleImageError = () => {
    setImageError(true);
  };

  // Determine what to display based on avatar type
  const renderAvatarContent = () => {
    if (!user) {
      return 'U';
    }

    // If image upload and no error, show image
    if (user.avatar?.type === 'upload' && user.avatar?.imageUrl && !imageError) {
      return (
        <img
          src={user.avatar.imageUrl}
          alt={`${user.username} avatar`}
          onError={handleImageError}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
        />
      );
    }

    // If icon selected, show icon
    if (user.avatar?.type === 'icon' && user.avatar?.iconName) {
      const IconComponent = AVATAR_ICONS[user.avatar.iconName];
      if (IconComponent) {
        return (
          <IconComponent 
            sx={{ 
              fontSize: size * 0.6,
              color: '#FFFFFF'
            }} 
          />
        );
      }
    }

    // Default to initials (or fallback from failed image)
    return getInitials(user);
  };

  const avatarSx = {
    bgcolor: user?.color || '#1976d2',
    width: size,
    height: size,
    cursor: onClick ? 'pointer' : 'default',
    fontSize: size * 0.4, // Responsive font size for initials
    fontWeight: 600,
    color: '#FFFFFF !important', // Force white color with !important to override any external styles
    '& .MuiAvatar-fallback': {
      color: '#FFFFFF !important'
    },
    // Add inactive styling
    ...(showInactive && {
      opacity: 0.6,
      position: 'relative',
      '&::after': {
        content: '""',
        position: 'absolute',
        top: '50%',
        left: '10%',
        right: '10%',
        height: '2px',
        backgroundColor: '#f44336',
        transform: 'translateY(-50%) rotate(-15deg)',
        zIndex: 1
      }
    }),
    ...sx
  };

  return (
    <Avatar
      onClick={onClick}
      sx={avatarSx}
      {...props}
    >
      {renderAvatarContent()}
    </Avatar>
  );
};

export default EnhancedAvatar;
