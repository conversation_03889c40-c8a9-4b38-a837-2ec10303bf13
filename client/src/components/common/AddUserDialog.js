import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  TextField,
  InputAdornment,
  IconButton,
  Box,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { Search as SearchIcon, Add as AddIcon } from '@mui/icons-material';
import EnhancedAvatar from './EnhancedAvatar';
import axios from 'axios';

const AddUserDialog = ({ open, onClose, onAdd, existingUsers = [], title = 'Add User', showApproverOption = false }) => {
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [userApproverStates, setUserApproverStates] = useState({}); // Track approver state per user

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/users');
        // Filter out users that are already members
        const existingUserIds = existingUsers.map(member => member.user._id);
        const filteredUsers = response.data.filter(user => !existingUserIds.includes(user._id));
        setUsers(filteredUsers);

        // Initialize approver states for all users (default to true)
        const initialApproverStates = {};
        filteredUsers.forEach(user => {
          initialApproverStates[user._id] = true;
        });
        setUserApproverStates(initialApproverStates);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchUsers();
    }
  }, [open, existingUsers]);

  const handleAdd = (user) => {
    const isApprover = showApproverOption ? (userApproverStates[user._id] || false) : undefined;
    if (showApproverOption) {
      onAdd(user._id, [], isApprover); // Pass userId, roles, and approver status
    } else {
      onAdd(user._id, []); // Pass only userId and roles for backward compatibility
    }
    onClose();
  };

  const handleApproverToggle = (userId, checked) => {
    setUserApproverStates(prev => ({
      ...prev,
      [userId]: checked
    }));
  };

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          margin="normal"
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <List>
          {filteredUsers.map((user) => (
            <ListItem
              key={user._id}
              sx={{
                display: 'flex',
                alignItems: 'center',
                py: 1,
                px: 2,
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <ListItemAvatar>
                <EnhancedAvatar
                  user={user}
                  size={40}
                  showInactive={user.groupStatus === 'inactive'}
                />
              </ListItemAvatar>
              <ListItemText
                primary={user.username}
                sx={{ flex: 1 }}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {showApproverOption && (
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={userApproverStates[user._id] || false}
                        onChange={(e) => handleApproverToggle(user._id, e.target.checked)}
                        color="primary"
                        onClick={(e) => e.stopPropagation()} // Prevent row click
                      />
                    }
                    label="Approver"
                    sx={{ mr: 1 }}
                  />
                )}
                <IconButton
                  edge="end"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAdd(user);
                  }}
                  color="primary"
                >
                  <AddIcon />
                </IconButton>
              </Box>
            </ListItem>
          ))}
          {filteredUsers.length === 0 && (
            <ListItem>
              <ListItemText primary="No users found" />
            </ListItem>
          )}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddUserDialog;