import React from 'react';
import { Box, AppBar, Toolbar, Typography, IconButton, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';
import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useGroup } from '../../contexts/GroupContext';
import { useNavigate } from 'react-router-dom';
import { Logout as LogoutIcon, AccountCircle as AccountCircleIcon, AdminPanelSettings as AdminIcon, SupervisorAccount as SuperUserIcon } from '@mui/icons-material';
import NavigationSidebar from '../navigation/NavigationSidebar';
import EnhancedAvatar from '../common/EnhancedAvatar';
import ProfileDialog from '../profile/ProfileDialog';
import AdministrationPanel from '../group/AdministrationPanel';
import SuperUserDashboard from '../admin/SuperUserDashboard';

const DRAWER_WIDTH = 300;

// Global state to persist across hot reloads
window.__profileDialogState = window.__profileDialogState || { open: false };

const MainLayout = ({ children }) => {
  const { user, logout } = useAuth();
  const { isAdmin } = useGroup();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [administrationPanelOpen, setAdministrationPanelOpen] = useState(false);
  const [superUserDashboardOpen, setSuperUserDashboardOpen] = useState(false);

  // Use global state to persist across hot reloads
  const [profileDialogOpen, setProfileDialogOpen] = useState(() => {
    return window.__profileDialogState.open;
  });

  // Sync local state with global state
  useEffect(() => {
    window.__profileDialogState.open = profileDialogOpen;
  }, [profileDialogOpen]);

  const handleProfileClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileClose = () => {
    setAnchorEl(null);
  };

  const handleChangeAvatar = () => {
    handleProfileClose();
    setProfileDialogOpen(true);
  };

  const handleAdministration = () => {
    handleProfileClose();
    setAdministrationPanelOpen(true);
  };

  const handleSuperUserDashboard = () => {
    handleProfileClose();
    setSuperUserDashboardOpen(true);
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
    handleProfileClose();
  };

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: '#1976d2',
          minHeight: 'auto',
          borderRadius: 0
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            padding: '2px 16px',
            minHeight: 'auto'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <img
              src="/LogoSunday3.svg"
              alt="Requirements Tool Logo"
              style={{
                height: '48px',
                width: 'auto',
                display: 'block',
                margin: 0,
                padding: 0,
                verticalAlign: 'top'
              }}
            />
          </Box>

          {user && (
            <>
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-controls="profile-menu"
                aria-haspopup="true"
                onClick={handleProfileClick}
                color="inherit"
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                <EnhancedAvatar user={user} size={32} />
              </IconButton>
              <Menu
                id="profile-menu"
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleProfileClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
              >
                <MenuItem onClick={handleChangeAvatar}>
                  <ListItemIcon>
                    <AccountCircleIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Change Avatar</ListItemText>
                </MenuItem>
                {isAdmin && (
                  <MenuItem onClick={handleAdministration}>
                    <ListItemIcon>
                      <AdminIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Administration</ListItemText>
                  </MenuItem>
                )}
                {user?.isSuperUser && (
                  <MenuItem onClick={handleSuperUserDashboard}>
                    <ListItemIcon>
                      <SuperUserIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Super User Dashboard</ListItemText>
                  </MenuItem>
                )}
                <MenuItem onClick={handleLogout}>
                  <ListItemIcon>
                    <LogoutIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Logout</ListItemText>
                </MenuItem>
              </Menu>
            </>
          )}
        </Box>
      </AppBar>

      {/* Navigation Sidebar */}
      {user && <NavigationSidebar />}

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 0,
          width: user ? `calc(100% - ${DRAWER_WIDTH}px)` : '100%',
          marginLeft: user ? 0 : 0,
          marginTop: '56px' // Account for compact banner height
        }}
      >
        {children}
      </Box>

      <ProfileDialog
        open={profileDialogOpen}
        onClose={() => setProfileDialogOpen(false)}
      />

      <AdministrationPanel
        open={administrationPanelOpen}
        onClose={() => setAdministrationPanelOpen(false)}
      />

      <SuperUserDashboard
        open={superUserDashboardOpen}
        onClose={() => setSuperUserDashboardOpen(false)}
      />
    </Box>
  );
};

export default MainLayout;
