import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import axios from 'axios';

const GroupContext = createContext();

export const useGroup = () => useContext(GroupContext);

export const GroupProvider = ({ children }) => {
  const [group, setGroup] = useState(null);
  const [groupMembers, setGroupMembers] = useState([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Load group data when user changes
  useEffect(() => {
    if (isAuthenticated && user && user.group) {
      loadGroupData();
    } else {
      setGroup(null);
      setGroupMembers([]);
      setIsAdmin(false);
    }
  }, [isAuthenticated, user]);

  const loadGroupData = async () => {
    try {
      setLoading(true);
      
      // Load group details
      const groupRes = await axios.get(`/api/groups/${user.group.id}`);
      setGroup(groupRes.data);

      // Load group members
      const membersRes = await axios.get(`/api/groups/${user.group.id}/members`);
      setGroupMembers(membersRes.data.members);

      // Check if current user is admin
      const userMembership = membersRes.data.members.find(
        member => member.user._id === user.id
      );
      setIsAdmin(userMembership?.role === 'administrator');

    } catch (error) {
      console.error('Error loading group data:', error);
    } finally {
      setLoading(false);
    }
  };

  const addGroupMembers = async (users, welcomeMessage) => {
    try {
      const response = await axios.post(`/api/groups/${group._id}/members`, {
        users,
        welcomeMessage
      });
      
      // Reload group members
      await loadGroupData();
      
      return response.data;
    } catch (error) {
      console.error('Error adding group members:', error);
      throw error;
    }
  };

  const removeGroupMember = async (userId) => {
    try {
      await axios.delete(`/api/groups/${group._id}/members/${userId}`);
      
      // Reload group members
      await loadGroupData();
    } catch (error) {
      console.error('Error removing group member:', error);
      throw error;
    }
  };

  const updateMemberRole = async (userId, role) => {
    try {
      await axios.put(`/api/groups/${group._id}/members/${userId}`, { role });
      
      // Reload group members
      await loadGroupData();
    } catch (error) {
      console.error('Error updating member role:', error);
      throw error;
    }
  };

  const updateGroupSettings = async (settings) => {
    try {
      const response = await axios.put(`/api/groups/${group._id}`, settings);
      setGroup(response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating group settings:', error);
      throw error;
    }
  };

  const generateGroupCode = async (groupName) => {
    try {
      const response = await axios.post('/api/groups/suggest-code', {
        name: groupName
      });
      return response.data;
    } catch (error) {
      console.error('Error generating group code:', error);
      throw error;
    }
  };

  const value = {
    group,
    groupMembers,
    isAdmin,
    loading,
    loadGroupData,
    addGroupMembers,
    removeGroupMember,
    updateMemberRole,
    updateGroupSettings,
    generateGroupCode
  };

  return (
    <GroupContext.Provider value={value}>
      {children}
    </GroupContext.Provider>
  );
};
