import React, { createContext, useState, useContext, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Set axios defaults
  axios.defaults.baseURL = 'http://localhost:5000';

  // Add axios interceptor to handle 401 responses
  useEffect(() => {
    const interceptor = axios.interceptors.response.use(
      response => response,
      error => {
        if (error.response?.status === 401) {
          // Clear auth state
          localStorage.removeItem('token');
          delete axios.defaults.headers.common['x-auth-token'];
          setUser(null);
          setIsAuthenticated(false);
          // Redirect to login
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, []);

  // Check for token and load user on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setLoading(false);
          setIsAuthenticated(false);
          return;
        }

        // Set the token in axios headers
        axios.defaults.headers.common['x-auth-token'] = token;

        // Load user data
        const res = await axios.get('/api/auth/user');
        setUser(res.data);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear invalid token
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['x-auth-token'];
        setUser(null);
        setIsAuthenticated(false);
        // Redirect to login
        window.location.href = '/login';
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (token, userData) => {
    try {
      if (!token || !userData) {
        throw new Error('Invalid login data');
      }

      // Store token in localStorage
      localStorage.setItem('token', token);

      // Set axios default headers
      axios.defaults.headers.common['x-auth-token'] = token;

      // Update state
      setUser(userData);
      setIsAuthenticated(true);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      // Clear any partial authentication state
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['x-auth-token'];
      setUser(null);
      setIsAuthenticated(false);
      return false;
    }
  };

  const register = async (registrationData) => {
    try {
      const res = await axios.post('/api/auth/register', registrationData);
      const { token, user } = res.data;
      await login(token, user);
      return { success: true, user };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Registration failed'
      };
    }
  };

  // Legacy register function for backward compatibility
  const registerLegacy = async (username, email, password, firstName = '', lastName = '') => {
    const result = await register({
      username,
      email,
      password,
      firstName,
      lastName,
      isNewGroup: true,
      groupName: 'Default Group',
      groupCode: 'default',
      tier: '1-3'
    });
    return result.success;
  };

  const logout = async () => {
    try {
      // Clear token from localStorage
      localStorage.removeItem('token');

      // Remove token from axios headers
      delete axios.defaults.headers.common['x-auth-token'];

      // Clear user state
      setUser(null);
      setIsAuthenticated(false);

      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  };

  const value = {
    user,
    setUser,
    isAuthenticated,
    loading,
    login,
    register,
    registerLegacy,
    logout,
    axios
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};