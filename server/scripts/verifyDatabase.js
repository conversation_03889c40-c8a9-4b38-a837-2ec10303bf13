const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function verifyDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n=== Collections in database ===');
    collections.forEach(collection => {
      console.log(`  - ${collection.name}`);
    });

    // Import models
    const User = require('../models/User');
    const Group = require('../models/Group');
    const GroupMembership = require('../models/GroupMembership');

    // Check users
    console.log('\n=== Users ===');
    const users = await User.find({});
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`  - ${user.username} (${user.email})`);
      console.log(`    Group ID: ${user.group}`);
      console.log(`    Group Status: ${user.groupStatus}`);
      console.log(`    Is Super User: ${user.isSuperUser}`);
    });

    // Check groups
    console.log('\n=== Groups ===');
    const groups = await Group.find({});
    console.log(`Found ${groups.length} groups:`);
    groups.forEach(group => {
      console.log(`  - ${group.name} (${group.code})`);
      console.log(`    ID: ${group._id}`);
      console.log(`    Tier: ${group.tier}`);
      console.log(`    Max Users: ${group.maxUsers}`);
      console.log(`    Created By: ${group.createdBy}`);
    });

    // Check group memberships
    console.log('\n=== Group Memberships ===');
    const memberships = await GroupMembership.find({}).populate('user').populate('group');
    console.log(`Found ${memberships.length} memberships:`);
    memberships.forEach(membership => {
      console.log(`  - User: ${membership.user?.username || 'Unknown'}`);
      console.log(`    Group: ${membership.group?.name || 'Unknown'}`);
      console.log(`    Role: ${membership.role}`);
      console.log(`    Status: ${membership.status}`);
    });

    // Verify relationships
    console.log('\n=== Relationship Verification ===');
    for (const user of users) {
      if (user.group) {
        const group = await Group.findById(user.group);
        if (group) {
          console.log(`✓ User ${user.username} -> Group ${group.name} (${group.code})`);
        } else {
          console.log(`✗ User ${user.username} -> Group ${user.group} (NOT FOUND)`);
        }

        const membership = await GroupMembership.findOne({ user: user._id, group: user.group });
        if (membership) {
          console.log(`✓ User ${user.username} has membership record`);
        } else {
          console.log(`✗ User ${user.username} missing membership record`);
        }
      }
    }

  } catch (error) {
    console.error('Error verifying database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
verifyDatabase();
