const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function initializeDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing collections (clean slate)
    console.log('Clearing existing collections...');
    
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    for (const collectionName of collectionNames) {
      if (!collectionName.startsWith('system.')) {
        await mongoose.connection.db.collection(collectionName).drop();
        console.log(`Dropped collection: ${collectionName}`);
      }
    }

    // Import models to ensure they're registered
    require('../models/User');
    require('../models/Group');
    require('../models/GroupMembership');
    require('../models/Project');
    require('../models/Feature');
    require('../models/Requirement');
    require('../models/Label');

    console.log('Models loaded successfully');

    // Create indexes by accessing the models
    const User = mongoose.model('User');
    const Group = mongoose.model('Group');
    const GroupMembership = mongoose.model('GroupMembership');
    const Project = mongoose.model('Project');
    const Feature = mongoose.model('Feature');
    const Requirement = mongoose.model('Requirement');
    const Label = mongoose.model('Label');

    // Ensure indexes are created
    await User.createIndexes();
    await Group.createIndexes();
    await GroupMembership.createIndexes();
    await Project.createIndexes();
    await Feature.createIndexes();
    await Requirement.createIndexes();
    await Label.createIndexes();

    console.log('Database indexes created successfully');

    // Verify collections were created
    const newCollections = await mongoose.connection.db.listCollections().toArray();
    console.log('Collections in database:');
    newCollections.forEach(collection => {
      console.log(`  - ${collection.name}`);
    });

    console.log('Database initialization completed successfully!');

  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
initializeDatabase();
