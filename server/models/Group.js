const mongoose = require('mongoose');

const groupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  tier: {
    type: String,
    enum: ['1-3', '4-25', '26-100', '100+'],
    required: true
  },
  maxUsers: {
    type: Number,
    required: true
  },
  welcomeMessage: {
    type: String,
    default: 'Welcome to our team! We\'re excited to have you on board.'
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Helper function to get max users based on tier
groupSchema.statics.getMaxUsersForTier = function(tier) {
  const tierLimits = {
    '1-3': 3,
    '4-25': 25,
    '26-100': 100,
    '100+': 1000 // Set a reasonable upper limit
  };
  return tierLimits[tier] || 3;
};

// Pre-save middleware to set maxUsers based on tier
groupSchema.pre('save', function(next) {
  if (this.isModified('tier') || this.isNew) {
    this.maxUsers = this.constructor.getMaxUsersForTier(this.tier);
  }
  this.updatedAt = Date.now();
  next();
});

// Method to generate group code from name
groupSchema.statics.generateCodeFromName = function(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

// Method to check if code is available
groupSchema.statics.isCodeAvailable = async function(code) {
  const existingGroup = await this.findOne({ code });
  return !existingGroup;
};

module.exports = mongoose.model('Group', groupSchema);
