const mongoose = require('mongoose');

const groupMembershipSchema = new mongoose.Schema({
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  role: {
    type: String,
    enum: ['member', 'administrator'],
    default: 'member'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    default: 'pending'
  },
  invitedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  joinedAt: {
    type: Date
  },
  invitedAt: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Compound index to ensure unique user-group combinations
groupMembershipSchema.index({ group: 1, user: 1 }, { unique: true });

// Method to activate membership (when user completes registration)
groupMembershipSchema.methods.activate = function() {
  this.status = 'active';
  this.joinedAt = new Date();
  return this.save();
};

// Method to deactivate membership
groupMembershipSchema.methods.deactivate = function() {
  this.status = 'inactive';
  return this.save();
};

// Static method to get active members count for a group
groupMembershipSchema.statics.getActiveMembersCount = async function(groupId) {
  return await this.countDocuments({ 
    group: groupId, 
    status: 'active' 
  });
};

// Static method to get all members for a group with user details
groupMembershipSchema.statics.getGroupMembersWithUsers = async function(groupId) {
  return await this.find({ group: groupId })
    .populate('user', 'username email firstName lastName avatar color')
    .populate('invitedBy', 'username')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model('GroupMembership', groupMembershipSchema);
