const mongoose = require('mongoose');

const labelSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
    // Note: uniqueness now enforced by compound index with group
  },
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  color: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index to ensure label name uniqueness within groups
labelSchema.index({ name: 1, group: 1 }, { unique: true });

module.exports = mongoose.model('Label', labelSchema);