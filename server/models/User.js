const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true
    // Note: uniqueness now enforced by compound index with group
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  groupStatus: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    default: 'pending'
  },
  isSuperUser: {
    type: Boolean,
    default: false
  },
  password: {
    type: String,
    required: true
  },
  firstName: {
    type: String,
    required: false,
    trim: true
  },
  lastName: {
    type: String,
    required: false,
    trim: true
  },
  color: {
    type: String,
    default: '#1976d2'  // Default to primary blue
  },
  avatar: {
    type: {
      type: String,
      enum: ['upload', 'icon', 'initials'],
      default: 'initials'
    },
    imageUrl: {
      type: String,
      default: null
    },
    iconName: {
      type: String,
      default: null
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index to ensure username uniqueness within groups
userSchema.index({ username: 1, group: 1 }, { unique: true });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to check if user is active in their group
userSchema.methods.isActive = function() {
  return this.groupStatus === 'active';
};

// Method to activate user
userSchema.methods.activate = function() {
  this.groupStatus = 'active';
  return this.save();
};

// Method to deactivate user
userSchema.methods.deactivate = function() {
  this.groupStatus = 'inactive';
  return this.save();
};

// Static method to find user by username and group code
userSchema.statics.findByUsernameAndGroup = async function(username, groupCode) {
  console.log('Finding user by username and group:', username, groupCode);

  // Use require here to avoid circular dependency issues
  const Group = require('./Group');
  const group = await Group.findOne({ code: groupCode });
  console.log('Found group:', group ? group.name : 'null');
  if (!group) return null;

  const user = await this.findOne({ username, group: group._id }).populate('group');
  console.log('Found user in group:', user ? user.username : 'null');
  return user;
};

// Static method to parse username@groupcode format
userSchema.statics.parseUsernameWithGroup = function(usernameWithGroup) {
  const parts = usernameWithGroup.split('@');
  if (parts.length !== 2) {
    throw new Error('Username must be in format: username@groupcode');
  }
  return {
    username: parts[0],
    groupCode: parts[1]
  };
};

module.exports = mongoose.model('User', userSchema);