const User = require('../models/User');

// Middleware to add group context to requests
const addGroupContext = async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      return next();
    }

    const user = await User.findById(req.user.userId).populate('group');
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user is active
    if (user.groupStatus !== 'active' && !user.isSuperUser) {
      return res.status(403).json({ message: 'Account is inactive' });
    }

    // Check if group is active
    if (user.group && user.group.status !== 'active' && !user.isSuperUser) {
      return res.status(403).json({ message: 'Group is inactive' });
    }

    // Add group context to request
    req.userGroup = user.group;
    req.isSuperUser = user.isSuperUser;
    
    next();
  } catch (error) {
    console.error('Error in group auth middleware:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Middleware to filter queries by group
const filterByGroup = (req, res, next) => {
  if (req.isSuperUser) {
    // Super users can see all data
    return next();
  }

  if (!req.userGroup) {
    return res.status(403).json({ message: 'No group context available' });
  }

  // Add group filter to query parameters
  req.groupFilter = { group: req.userGroup._id };
  
  next();
};

// Helper function to add group to creation data
const addGroupToData = (req, data) => {
  if (req.isSuperUser && data.group) {
    // Super user can specify group explicitly
    return data;
  }
  
  if (!req.userGroup) {
    throw new Error('No group context available');
  }

  return {
    ...data,
    group: req.userGroup._id
  };
};

// Middleware to ensure user can only access their group's data
const ensureGroupAccess = (Model) => {
  return async (req, res, next) => {
    try {
      if (req.isSuperUser) {
        return next();
      }

      const resourceId = req.params.id;
      if (!resourceId) {
        return next();
      }

      const resource = await Model.findById(resourceId);
      if (!resource) {
        return res.status(404).json({ message: 'Resource not found' });
      }

      // Check if resource belongs to user's group
      if (resource.group && resource.group.toString() !== req.userGroup._id.toString()) {
        return res.status(403).json({ message: 'Access denied' });
      }

      next();
    } catch (error) {
      console.error('Error in group access middleware:', error);
      res.status(500).json({ message: 'Server error' });
    }
  };
};

module.exports = {
  addGroupContext,
  filterByGroup,
  addGroupToData,
  ensureGroupAccess
};
