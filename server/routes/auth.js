const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const auth = require('../middleware/auth');

// Register a new user (multi-tenant)
router.post('/register', async (req, res) => {
  try {
    const {
      username,
      password,
      email,
      firstName,
      lastName,
      groupName,
      groupCode,
      tier,
      isNewGroup
    } = req.body;

    const Group = require('../models/Group');
    const GroupMembership = require('../models/GroupMembership');

    let group;
    let user;

    if (isNewGroup) {
      // Creating a new group
      if (!groupName || !groupCode || !tier) {
        return res.status(400).json({ message: 'Group name, code, and tier are required for new groups' });
      }

      // Check if group code is available
      const existingGroup = await Group.findOne({ code: groupCode });
      if (existingGroup) {
        return res.status(400).json({ message: 'Group code already exists' });
      }

      // Create new user as administrator first
      user = new User({
        username,
        password,
        email,
        firstName: firstName || '',
        lastName: lastName || '',
        groupStatus: 'active'
      });

      await user.save();

      // Create the group with the user as creator
      group = new Group({
        name: groupName,
        code: groupCode,
        tier,
        createdBy: user._id
      });

      await group.save();

      // Update user with group reference
      user.group = group._id;
      await user.save();

      // Create group membership as administrator
      const membership = new GroupMembership({
        group: group._id,
        user: user._id,
        role: 'administrator',
        status: 'active',
        joinedAt: new Date()
      });

      await membership.save();

    } else {
      // Joining existing group
      if (!groupCode) {
        return res.status(400).json({ message: 'Group code is required' });
      }

      // Find the group
      group = await Group.findOne({ code: groupCode });
      if (!group) {
        return res.status(400).json({ message: 'Group not found' });
      }

      // Check if username is available in this group
      const existingUser = await User.findOne({ username, group: group._id });
      if (!existingUser) {
        return res.status(400).json({ message: 'Username not found in this group. Please contact your administrator.' });
      }

      // Check if user is in pending state
      if (existingUser.groupStatus !== 'pending') {
        return res.status(400).json({ message: 'User account is not in pending state' });
      }

      // Update the existing user with password and complete registration
      existingUser.password = password;
      existingUser.groupStatus = 'active';
      await existingUser.save();

      // Activate group membership
      const membership = await GroupMembership.findOne({
        group: group._id,
        user: existingUser._id
      });

      if (membership) {
        await membership.activate();
      }

      user = existingUser;
    }

    // Create JWT token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(201).json({
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        color: user.color,
        avatar: user.avatar,
        group: {
          id: group._id,
          name: group.name,
          code: group.code
        }
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Login user (multi-tenant)
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('Login attempt for user:', username);

    let user;
    let group;

    // Check if username contains @ (username@groupcode format)
    if (username.includes('@')) {
      try {
        console.log('Parsing username with group:', username);
        const { username: parsedUsername, groupCode } = User.parseUsernameWithGroup(username);
        console.log('Parsed username:', parsedUsername, 'groupCode:', groupCode);

        user = await User.findByUsernameAndGroup(parsedUsername, groupCode);
        console.log('Found user:', user ? user.username : 'null');

        if (user) {
          group = user.group;
          console.log('User group:', group ? group.name : 'null');
        }
      } catch (error) {
        console.log('Error parsing username format:', error.message);
        console.log('Username that failed:', username);
        return res.status(400).json({ message: 'Invalid username format' });
      }
    } else {
      // Legacy support or super user login
      user = await User.findOne({ username }).populate('group');
      if (user) {
        group = user.group;
      }
    }

    if (!user) {
      console.log('User not found:', username);
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Check if user is active (unless super user)
    if (!user.isSuperUser && user.groupStatus !== 'active') {
      console.log('User account inactive:', username);
      return res.status(403).json({ message: 'Account is inactive' });
    }

    // Check if group is active (unless super user)
    if (!user.isSuperUser && group && group.status !== 'active') {
      console.log('Group inactive for user:', username);
      return res.status(403).json({ message: 'Group is inactive' });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      console.log('Password mismatch for user:', username);
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    console.log('User authenticated successfully:', username);

    // Create JWT token
    const tokenPayload = { userId: user._id };
    console.log('Creating token with payload:', tokenPayload);

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('Token created successfully');

    const response = {
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        color: user.color,
        avatar: user.avatar,
        isSuperUser: user.isSuperUser,
        group: group ? {
          id: group._id,
          name: group.name,
          code: group.code
        } : null
      }
    };

    console.log('Sending response:', response);
    res.json(response);
  } catch (error) {
    console.error('Login error:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({ message: 'Server error' });
  }
});

// Get current user
router.get('/user', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId)
      .select('-password')
      .populate('group', 'name code status');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      id: user._id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      color: user.color,
      avatar: user.avatar,
      isSuperUser: user.isSuperUser,
      groupStatus: user.groupStatus,
      group: user.group ? {
        id: user.group._id,
        name: user.group.name,
        code: user.group.code,
        status: user.group.status
      } : null
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;