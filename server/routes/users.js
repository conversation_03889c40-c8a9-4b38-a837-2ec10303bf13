const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');
const User = require('../models/User');
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup } = require('../middleware/groupAuth');
const upload = require('../middleware/upload');

// Register a new user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName } = req.body;

    // Check if user already exists
    let user = await User.findOne({ email });
    if (user) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create new user
    user = new User({
      username,
      email,
      password,
      firstName: firstName || '',
      lastName: lastName || ''
    });

    // Hash password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);

    // Save user
    await user.save();

    // Create and return JWT token
    const payload = {
      user: {
        id: user.id
      }
    };

    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '5 days' },
      (err, token) => {
        if (err) throw err;
        res.json({ token });
      }
    );
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Create and return JWT token
    const payload = {
      user: {
        id: user.id
      }
    };

    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '5 days' },
      (err, token) => {
        if (err) throw err;
        res.json({ token });
      }
    );
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// Get current user
router.get('/me', async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// Get all users (filtered by group)
router.get('/', auth, addGroupContext, async (req, res) => {
  try {
    let filter = {};

    if (!req.isSuperUser) {
      // Regular users only see active users in their group
      filter = {
        group: req.userGroup._id,
        groupStatus: 'active'
      };
    }
    // Super users see all users

    const users = await User.find(filter, 'username color firstName lastName avatar groupStatus')
      .populate('group', 'name code');
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Upload avatar image
router.post('/avatar/upload', auth, upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const imageUrl = `/api/users/avatar/${req.file.filename}`;

    // Update user's avatar
    await User.findByIdAndUpdate(req.user.userId, {
      'avatar.type': 'upload',
      'avatar.imageUrl': imageUrl,
      'avatar.iconName': null
    });

    res.json({
      message: 'Avatar uploaded successfully',
      imageUrl: imageUrl
    });
  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({ message: 'Server error during upload' });
  }
});

// Update avatar settings (for icon selection or switching to initials)
router.put('/avatar', auth, async (req, res) => {
  try {
    const { type, iconName } = req.body;

    const updateData = {
      'avatar.type': type
    };

    if (type === 'icon') {
      updateData['avatar.iconName'] = iconName;
      updateData['avatar.imageUrl'] = null;
    } else if (type === 'initials') {
      updateData['avatar.iconName'] = null;
      updateData['avatar.imageUrl'] = null;
    }

    const user = await User.findByIdAndUpdate(
      req.user.userId,
      updateData,
      { new: true }
    ).select('-password');

    res.json(user);
  } catch (error) {
    console.error('Avatar update error:', error);
    res.status(500).json({ message: 'Server error during avatar update' });
  }
});

// Serve avatar images
router.get('/avatar/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const imagePath = path.join(__dirname, '../uploads/avatars', filename);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({ message: 'Image not found' });
    }

    res.sendFile(imagePath);
  } catch (error) {
    console.error('Avatar serve error:', error);
    res.status(500).json({ message: 'Server error serving image' });
  }
});

// Update user profile (including firstName, lastName, color)
router.put('/profile', auth, async (req, res) => {
  try {
    const { firstName, lastName, username, email, color } = req.body;

    const updateData = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (color !== undefined) updateData.color = color;

    const user = await User.findByIdAndUpdate(
      req.user.userId,
      updateData,
      { new: true }
    ).select('-password');

    res.json(user);
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ message: 'Server error during profile update' });
  }
});

module.exports = router;