const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup, addGroupToData } = require('../middleware/groupAuth');
const Label = require('../models/Label');

// Get all labels
router.get('/', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    const filter = req.groupFilter || {};
    const labels = await Label.find(filter).sort({ name: 1 });
    res.json(labels);
  } catch (error) {
    console.error('Error fetching labels:', error);
    res.status(500).json({ message: error.message });
  }
});

// Create a new label
router.post('/', auth, addGroupContext, async (req, res) => {
  try {
    const { name } = req.body;

    // Generate a random color
    const color = '#' + Math.floor(Math.random()*16777215).toString(16);

    const labelData = addGroupToData(req, {
      name,
      color
    });

    const label = new Label(labelData);
    const newLabel = await label.save();
    res.status(201).json(newLabel);
  } catch (error) {
    console.error('Error creating label:', error);
    res.status(400).json({ message: error.message });
  }
});

// Delete a label
router.delete('/:id', auth, async (req, res) => {
  try {
    const label = await Label.findById(req.params.id);
    if (!label) {
      return res.status(404).json({ message: 'Label not found' });
    }
    
    await label.remove();
    res.json({ message: 'Label deleted' });
  } catch (error) {
    console.error('Error deleting label:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router; 