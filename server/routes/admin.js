const express = require('express');
const router = express.Router();
const Group = require('../models/Group');
const User = require('../models/User');
const GroupMembership = require('../models/GroupMembership');
const auth = require('../middleware/auth');

// Middleware to check super user status
const requireSuperUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isSuperUser) {
      return res.status(403).json({ message: 'Super user access required' });
    }
    next();
  } catch (error) {
    console.error('Error checking super user status:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all groups (super user only)
router.get('/groups', auth, requireSuperUser, async (req, res) => {
  try {
    const groups = await Group.find()
      .populate('createdBy', 'username email firstName lastName')
      .sort({ createdAt: -1 });

    // Get member counts for each group
    const groupsWithCounts = await Promise.all(
      groups.map(async (group) => {
        const totalMembers = await GroupMembership.countDocuments({ group: group._id });
        const activeMembers = await GroupMembership.getActiveMembersCount(group._id);
        
        return {
          ...group.toObject(),
          totalMembers,
          activeMembers,
          availableSeats: group.maxUsers - activeMembers
        };
      })
    );

    res.json(groupsWithCounts);
  } catch (error) {
    console.error('Error fetching groups:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get specific group details (super user only)
router.get('/groups/:id', auth, requireSuperUser, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id)
      .populate('createdBy', 'username email firstName lastName');

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Get detailed member information
    const members = await GroupMembership.getGroupMembersWithUsers(group._id);
    const totalMembers = members.length;
    const activeMembers = members.filter(m => m.status === 'active').length;

    res.json({
      ...group.toObject(),
      members,
      totalMembers,
      activeMembers,
      availableSeats: group.maxUsers - activeMembers
    });
  } catch (error) {
    console.error('Error fetching group details:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Activate/Deactivate group (super user only)
router.put('/groups/:id/status', auth, requireSuperUser, async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!['active', 'inactive'].includes(status)) {
      return res.status(400).json({ message: 'Status must be active or inactive' });
    }

    const group = await Group.findById(req.params.id);
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    const oldStatus = group.status;
    group.status = status;
    await group.save();

    // If deactivating group, deactivate all users
    if (status === 'inactive') {
      await User.updateMany(
        { group: group._id },
        { groupStatus: 'inactive' }
      );
      
      await GroupMembership.updateMany(
        { group: group._id },
        { status: 'inactive' }
      );
    } 
    // If reactivating group, reactivate users who were active before
    else if (status === 'active' && oldStatus === 'inactive') {
      // For now, reactivate all users. In a more sophisticated system,
      // we might track who was individually deactivated vs group deactivated
      await User.updateMany(
        { group: group._id },
        { groupStatus: 'active' }
      );
      
      await GroupMembership.updateMany(
        { group: group._id },
        { status: 'active' }
      );
    }

    res.json({ 
      message: `Group ${status === 'active' ? 'activated' : 'deactivated'} successfully`,
      group 
    });
  } catch (error) {
    console.error('Error updating group status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get system statistics (super user only)
router.get('/stats', auth, requireSuperUser, async (req, res) => {
  try {
    const totalGroups = await Group.countDocuments();
    const activeGroups = await Group.countDocuments({ status: 'active' });
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ groupStatus: 'active' });
    const pendingUsers = await User.countDocuments({ groupStatus: 'pending' });

    // Group tier distribution
    const tierDistribution = await Group.aggregate([
      {
        $group: {
          _id: '$tier',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      totalGroups,
      activeGroups,
      inactiveGroups: totalGroups - activeGroups,
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers - pendingUsers,
      pendingUsers,
      tierDistribution
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
